{"name": "@deepsearch/server", "version": "0.1.0", "description": "DeepSearch 后端服务 (NestJS)", "main": "dist/server/src/main.js", "private": true, "scripts": {"prepare": "echo 'server package prepare'", "dev": "export NODE_ENV=development;nest start --watch", "start": "export NODE_ENV=development;node dist/server/src/main.js", "build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "check-env": "node scripts/check-env.js", "test-ai": "node scripts/test-ai-service.js", "debug": "nest start --debug --watch", "tcp-dev": "node test-tcp-dev.js", "start:dev": "export NODE_ENV=development;nest start --watch"}, "dependencies": {"@deepsearch/shared": "workspace:*", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/node": "^18.16.0", "@types/uuid": "^9.0.1", "eslint": "^8.39.0", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}