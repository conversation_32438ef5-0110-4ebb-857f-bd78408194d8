import { Injectable, On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '../config/config.service';
import { ProjectService } from './project.service';

// 消息接口定义
export interface Message<T = any> {
  messageType: string;
  messageId: string;
  data: T;
  common?: any;
}

// 协议定义
export interface IProtocol {
  [key: string]: [any, any]; // [requestType, responseType]
}

// 从核心服务到IDE的协议
export interface ToIdeProtocol extends IProtocol {
  'assistant/agent/message': [any, { status: string }];
  'assistant/agent/messageList': [any[], { status: string }];
  'assistant/agent/apiConversationList': [any[], { status: string }];
  'assistant/agent/writeToFile': [{ path: string; content: string }, { status: string; data?: any }];
  'assistant/agent/executeCommand': [{ command: string; is_background: boolean }, { status: string; data?: any }];
  'assistant/agent/environment': [{ includeFileDetails: boolean }, { status: string; data?: any }];
  'assistant/agent/editFile': [
    { path: string; content: string; language: string; instructions: string },
    { status: string; data?: any; type?: string }
  ];
  'state/agentState': [undefined, { status: string; data?: { isPaused?: boolean } }];
  'state/checkRepoState': [
    undefined,
    { status: string; data?: { commitId?: string; progress?: number; isPaused?: boolean } }
  ];
  'index/progress': [{ progress: number; indexed: boolean }, { status: string }];
}

// 从IDE到核心服务的协议
export interface FromIdeProtocol extends IProtocol {
  'assistant/agent/local': [any, any];
  'assistant/agent/getDiffSet': [{ sessionId: string; lhsHash: string; rhsHash?: string }, { status: string; data?: any }];
  'index/file': [{ file: string; action: 'create' | 'modify' | 'delete' }, void];
  'index/repoIndex': [undefined, void];
  'index/build': [undefined, void];
  'index/clearIndex': [undefined, void];
  'search/search': [{ query: string; limit?: number }, { status: string; data?: any }];
  'state/agentState': [undefined, { status: string; data?: { isPaused?: boolean } }];
  'state/checkRepoState': [
    undefined,
    { status: string; data?: { commitId?: string; progress?: number; isPaused?: boolean } }
  ];
  'state/ideState': [undefined, { status: 'ok' }];
  'state/ideInfo': [
    undefined,
    {
      status: string;
      data?: {
        pluginVersion: string;
        version: string;
        platform: string;
        device: string;
        repoInfo: {
          git_url: string;
          dir_path: string;
          commit: string;
          branch: string;
        };
        userInfo: {
          name: string;
        };
        proxyUrl: string;
        maxIndexSpace: number;
        cwd: string;
      };
    }
  ];
  'config/getIdeSetting': [
    undefined,
    {
      status: string;
      data?: {
        dirPath: string;
        fileRetryTime: number;
        modelRetryTime: number;
        enableRepoIndex: boolean;
        maxIndexSpace: number;
        proxyUrl: string;
        agentPreference: string;
      };
    }
  ];
}

// 事件处理器类型
type EventHandler = (...args: any[]) => void;

/**
 * 本地服务类 - 负责与二进制服务通信
 */
@Injectable()
export class LocalService implements OnModuleDestroy {
  private messenger: MessengerService | null = null;
  private subprocess: ChildProcessWithoutNullStreams | null = null;
  private isShuttingDown = false;
  private readonly logger = new Logger('LocalService');
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private restartAttempts = 0;
  private readonly MAX_RESTART_ATTEMPTS = 10;
  private events = new Map<string, EventHandler[]>();

  constructor(
    private readonly configService: ConfigService,
    private readonly projectService: ProjectService
  ) {
    this.startSubprocess().catch((reason) => {
      this.logger.error(`启动本地服务失败: ${reason}`, reason);
    });
    this.startHealthCheck();
  }

  onModuleDestroy() {
    this.dispose();
  }

  dispose() {
    this.isShuttingDown = true;
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.subprocess) {
      this.subprocess.kill();
    }
    if (this.messenger) {
      this.messenger.dispose();
    }
  }

  private async startSubprocess() {
    try {
      const binaryPath = this.configService.getBinaryPath();
      const cwd = this.configService.getBinaryCwd();
      
      this.logger.log(`启动二进制服务: ${binaryPath}`);
      this.subprocess = spawn(binaryPath, {
        env: {
          ...process.env,
          PWD: cwd,
        },
        cwd,
      });
      
      this.subprocess.stdout.on('data', (data) => {
        this.logger.debug(`服务输出: ${data}`);
      });
      
      this.subprocess.stderr.on('data', (data) => {
        this.logger.error(`服务错误: ${data}`);
      });
      
      this.setupSubprocessListeners();
      this.messenger = new MessengerService(this.subprocess, this.projectService);
      
      this.logger.log('二进制服务启动成功');
      this.emit('ready');
    } catch (error) {
      this.logger.error('二进制服务启动失败', error);
      throw error;
    }
  }

  private setupSubprocessListeners() {
    if (!this.subprocess) return;
    
    this.subprocess.on('exit', (code, signal) => {
      this.logger.warn(`子进程退出: code=${code}, signal=${signal}`);
      if (!this.isShuttingDown) {
        this.restartSubprocess();
      }
    });
  }

  private startHealthCheck() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        if (!this.messenger) return;
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('健康检查超时')), 3 * 60 * 1000);
        });

        const pingPromise = this.request('state/agentState', undefined);
        await Promise.race([pingPromise, timeoutPromise]);
      } catch (error) {
        if (this.messenger?.checkIsRunning()) {
          return;
        }
        this.logger.error('健康检查失败', error);
        await this.restartSubprocess();
      }
    }, 10 * 60 * 1000); // 每10分钟检查一次
  }
  private async restartSubprocess() {
    this.logger.log('重启子进程');

    if (this.restartAttempts >= this.MAX_RESTART_ATTEMPTS) {
      this.logger.error('达到最大重试次数，放弃重启');
      this.isShuttingDown = true;
      return;
    }

    this.restartAttempts++;
    try {
      // 清理旧进程
      if (this.subprocess) {
        this.subprocess.kill('SIGKILL');
      }
      if (this.messenger) {
        this.messenger.dispose();
      }
      
      // 重新启动
      await this.startSubprocess();
      this.emit('restart');
    } catch (error) {
      this.logger.error('重启子进程失败', error);
    }
  }

  // 事件注册和触发
  on(event: string, handler: EventHandler) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)?.push(handler);
    return this;
  }

  private emit(event: string, ...args: any[]) {
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(...args));
    }
  }

  // 消息处理方法
  onMessage<T extends keyof ToIdeProtocol>(
    messageType: T,
    handler: (message: Message<ToIdeProtocol[T][0]>) => ToIdeProtocol[T][1]
  ) {
    this.logger.debug(`🔧 注册消息处理器: ${messageType as string}`);
    if (!this.messenger) {
      this.logger.warn(`⚠️ Messenger 未初始化，无法注册处理器: ${messageType as string}`);
      return;
    }
    this.messenger.on(messageType, handler);
  }

  sendMessage<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0],
    messageId?: string
  ): string {
    if (!this.messenger) throw new Error('服务未初始化');
    return this.messenger.send(messageType, data, messageId);
  }

  request<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0]
  ): Promise<FromIdeProtocol[T][1]> {
    if (!this.messenger) throw new Error('服务未初始化');
    return this.messenger.request(messageType, data);
  }

  checkIsRunning(): boolean {
    return !!this.messenger?.checkIsRunning();
  }
}

/**
 * 消息服务 - 处理与二进制服务的通信
 */
class MessengerService {
  private readonly logger = new Logger('MessengerService');
  private typeListeners = new Map<string, ((message: Message) => any)[]>();
  private idListeners = new Map<string, (message: Message) => any>();
  private lastMessageTime = Date.now();
  private dataBuffer = '';

  constructor(
    private readonly subprocess: ChildProcessWithoutNullStreams,
    private readonly projectService: ProjectService
  ) {
    this.setupListeners();
  }

  private setupListeners() {
    this.subprocess.stdout.on('data', (data: Buffer) => {
      this.handleData(data);
    });
  }

  private handleData(data: Buffer) {
    this.lastMessageTime = Date.now();
    this.dataBuffer += data.toString();
    
    let newlineIndex: number;
    // 处理可能的多行输出
    while ((newlineIndex = this.dataBuffer.indexOf('\n')) !== -1) {
      const line = this.dataBuffer.substring(0, newlineIndex).trim();
      this.dataBuffer = this.dataBuffer.substring(newlineIndex + 1);
      
      if (line) {
        this.handleLine(line);
      }
    }
  }

  private handleLine(line: string) {
    try {
      const msg: Message = JSON.parse(line);

      if (!msg.messageType || !msg.messageId) {
        throw new Error(`无效消息格式: ${line}`);
      }

      this.logger.debug(`📨 收到消息: ${msg.messageType} (ID: ${msg.messageId})`);

      // 处理注册的消息类型监听器
      const listeners = this.typeListeners.get(msg.messageType);
      if (listeners?.length) {
        this.logger.debug(`🎯 找到 ${listeners.length} 个监听器处理 ${msg.messageType}`);
        listeners.forEach(async (handler) => {
          try {
            const response = await handler(msg);
            if (response) {
              this.logger.debug(`📤 发送响应: ${msg.messageType} -> ${JSON.stringify(response).slice(0, 100)}...`);
              this.send(msg.messageType, response, msg.messageId);
            }
          } catch (e) {
            this.logger.error(`处理消息错误: ${msg.messageType}`, e);
          }
        });
      } else {
        this.logger.warn(`⚠️ 没有找到处理器: ${msg.messageType}`);
        this.logger.debug(`当前注册的消息类型: ${Array.from(this.typeListeners.keys()).join(', ')}`);
      }

      // 处理等待响应的消息ID监听器
      const idHandler = this.idListeners.get(msg.messageId);
      if (idHandler) {
        this.logger.debug(`🔄 处理响应消息: ${msg.messageId}`);
        idHandler(msg);
        this.idListeners.delete(msg.messageId);
      }
    } catch (e) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + '...' + line.substring(line.length - 100);
      }
      this.logger.error(`解析消息失败: ${truncatedLine}`, e);
    }
  }

  on<T extends keyof ToIdeProtocol>(
    messageType: T,
    handler: (message: Message<ToIdeProtocol[T][0]>) => ToIdeProtocol[T][1]
  ) {
    const msgType = messageType as string;
    this.logger.debug(`📝 MessengerService 注册处理器: ${msgType}`);

    if (!this.typeListeners.has(msgType)) {
      this.typeListeners.set(msgType, []);
    }
    this.typeListeners.get(msgType)?.push(handler);

    this.logger.debug(`📊 ${msgType} 现有处理器数量: ${this.typeListeners.get(msgType)?.length}`);
  }

  send<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0],
    messageId?: string
  ): string {
    messageId = messageId ?? uuidv4();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId,
      common: this.getCommonMessage(),
    };

    const jsonMsg = JSON.stringify(msg) + '\n';
    this.subprocess.stdin.write(jsonMsg);
    return messageId;
  }

  request<T extends keyof FromIdeProtocol>(
    messageType: T,
    data: FromIdeProtocol[T][0]
  ): Promise<FromIdeProtocol[T][1]> {
    const messageId = uuidv4();
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.idListeners.delete(messageId);
        reject(new Error(`请求超时: ${messageType}`));
      }, 30000); // 30秒超时
      
      const handler = (msg: Message) => {
        clearTimeout(timeout);
        resolve(msg.data);
      };
      
      this.idListeners.set(messageId, handler);
      this.send(messageType, data, messageId);
    });
  }

  checkIsRunning(): boolean {
    return Date.now() - this.lastMessageTime < 5000; // 5秒内有消息则认为服务正在运行
  }

  dispose() {
    this.typeListeners.clear();
    this.idListeners.clear();
  }

  private getCommonMessage() {
    const projectInfo = this.projectService.getProjectInfo();
    return {
      version: '1.0.0',
      platform: 'deepsearch',
      cwd: process.cwd(),
      device: 'deepsearch-server',
      repo: {
        git_url: projectInfo?.gitRemote || '',
        dir_path: projectInfo?.repoPath || '',
        commit: projectInfo?.currentCommit || '',
      },
    };
  }
}
