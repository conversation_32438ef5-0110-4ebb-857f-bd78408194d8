import { Injectable, Logger } from '@nestjs/common';
import { LocalService } from './local-service';
import * as uuid from 'uuid';
import {
  Message,
  Session,
  SessionConfig
} from '@deepsearch/shared';

/**
 * 对话服务 - 负责处理用户与AI的对话
 */
@Injectable()
export class ComposerService {
  private readonly logger = new Logger(ComposerService.name);
  private sessions: Map<string, Session> = new Map();
  private readonly defaultSystemPrompt = 
    '你是一个有用的AI助手，专注于回答用户的问题，并提供准确的信息。' + 
    '如果你不知道某个问题的答案，请直接说不知道，不要编造信息。';

  constructor(private readonly localService: LocalService) {
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    // 处理来自二进制服务的消息更新
    this.localService.onMessage('assistant/agent/message', (message) => {
      const data = message.data;
      if (data && data.sessionId && data.text) {
        this.logger.debug(`收到部分消息: ${data.sessionId} - ${data.text.slice(0, 50)}...`);
      }
      return { status: 'ok' };
    });

    // 处理完整消息列表更新
    this.localService.onMessage('assistant/agent/messageList', (message) => {
      const messages = message.data;
      if (messages && messages.length > 0) {
        const sessionId = messages[0].sessionId;
        this.logger.debug(`收到消息列表更新: ${sessionId}, ${messages.length}条消息`);
      }
      return { status: 'ok' };
    });

    // 处理文件编辑请求
    this.localService.onMessage('assistant/agent/editFile', (message) => {
      const { path, content, language, instructions } = message.data || {};
      this.logger.debug(`收到文件编辑请求: ${path}`);
      
      // 使用同步返回方式，在后台处理异步操作
      this.handleFileEdit(path, content, language, instructions)
        .then(() => this.logger.debug(`文件编辑处理完成: ${path}`))
        .catch(error => this.logger.error(`文件编辑处理失败: ${path}`, error));
      
      // 立即返回成功响应
      return {
        status: 'ok',
        data: { type: 'success', path }
      };
    });

    // 处理命令执行请求
    this.localService.onMessage('assistant/agent/executeCommand', (message) => {
      const { command, is_background } = message.data || {};
      this.logger.debug(`收到命令执行请求: ${command}`);
      
      // 使用同步返回方式，在后台处理异步操作
      this.handleCommandExecution(command, is_background)
        .then(result => this.logger.debug(`命令执行完成: ${command}`, result))
        .catch(error => this.logger.error(`命令执行失败: ${command}`, error));
      
      // 立即返回成功响应
      return {
        status: 'ok',
        data: {
          userRejected: false,
          result: `正在执行命令: ${command}`,
          completed: false
        }
      };
    });

    // 处理环境信息请求
    this.localService.onMessage('assistant/agent/environment', (message) => {
      const { includeFileDetails } = message.data || {};
      this.logger.debug(`收到环境信息请求: includeFileDetails=${includeFileDetails}`);
      
      // 使用同步返回方式，在后台收集环境信息
      this.collectEnvironmentInfo(includeFileDetails)
        .then(info => this.logger.debug('环境信息收集完成'))
        .catch(error => this.logger.error('环境信息收集失败', error));
      
      // 立即返回基本环境信息
      return {
        status: 'ok',
        data: `<environment_details>\n# 当前工作目录\n${process.cwd()}\n\n# 当前时间\n${new Date().toLocaleString()}\n</environment_details>`
      };
    });
  }

  // 处理文件编辑的异步方法
  private async handleFileEdit(
    path: string,
    content: string,
    language: string,
    instructions: string
  ): Promise<void> {
    try {
      // 在这里实现实际的文件编辑逻辑
      this.logger.log(`处理文件编辑: ${path}`);
      // 例如: await fs.writeFile(path, content);
    } catch (error) {
      this.logger.error(`文件编辑失败: ${path}`, error);
      throw error;
    }
  }
  
  // 处理命令执行的异步方法
  private async handleCommandExecution(
    command: string,
    is_background: boolean
  ): Promise<any> {
    try {
      // 在这里实现实际的命令执行逻辑
      this.logger.log(`执行命令: ${command}, 后台运行: ${is_background}`);
      // 例如: const result = await exec(command);
      return { completed: true, result: `模拟执行命令结果: ${command}` };
    } catch (error) {
      this.logger.error(`命令执行失败: ${command}`, error);
      throw error;
    }
  }
  
  // 收集环境信息的异步方法
  private async collectEnvironmentInfo(includeFileDetails: boolean): Promise<string> {
    try {
      // 在这里实现实际的环境信息收集逻辑
      this.logger.log(`收集环境信息: includeFileDetails=${includeFileDetails}`);
      // 例如: const files = includeFileDetails ? await listFiles() : [];
      return `<environment_details>\n# 详细环境信息\n${process.cwd()}\n\n# 当前时间\n${new Date().toLocaleString()}\n</environment_details>`;
    } catch (error) {
      this.logger.error('收集环境信息失败', error);
      throw error;
    }
  }

  /**
   * 创建新会话
   */
  createSession(config: Partial<SessionConfig> = {}): Session {
    const id = uuid.v4();
    const now = Date.now();
    const session: Session = {
      id,
      messages: [],
      config: {
        model: config.model || 'gpt-3.5-turbo',
        temperature: config.temperature || 0.7,
        contextItems: config.contextItems || [],
        systemPrompt: config.systemPrompt || this.defaultSystemPrompt,
      },
      createdAt: now,
      updatedAt: now,
    };

    // 添加系统消息
    if (session.config.systemPrompt) {
      session.messages.push({
        role: 'system',
        content: session.config.systemPrompt,
        id: uuid.v4(),
        createdAt: now,
      });
    }

    this.sessions.set(id, session);
    return session;
  }

  /**
   * 获取会话
   */
  getSession(id: string): Session | undefined {
    return this.sessions.get(id);
  }

  /**
   * 获取所有会话
   */
  getAllSessions(): Session[] {
    return Array.from(this.sessions.values());
  }

  /**
   * 删除会话
   */
  deleteSession(id: string): boolean {
    return this.sessions.delete(id);
  }

  /**
   * 发送消息并获取回答
   */
  async sendMessage(sessionId: string, content: string): Promise<Message> {
    // 获取或创建会话
    let session = this.getSession(sessionId);
    if (!session) {
      session = this.createSession();
      sessionId = session.id;
    }

    const userMessageId = uuid.v4();
    const now = Date.now();
    
    // 添加用户消息
    const userMessage: Message = {
      role: 'user',
      content,
      id: userMessageId,
      createdAt: now,
    };
    
    session.messages.push(userMessage);
    session.updatedAt = now;
    try {
      // 发送请求到本地二进制服务
      this.logger.debug(`发送消息到AI服务: ${sessionId}, ${content.slice(0, 50)}...`);

      try {
        // 发送请求给本地二进制服务，参考 VSCode 插件的参数格式
        const response = await this.localService.request('assistant/agent/local', {
          type: 'newTask',
          task: content, // 用户输入的问题
          reqData: {
            sessionId,
            chatId: uuid.v4(),
          },
          rules: [], // 规则列表，暂时为空
          editorState: null, // 编辑器状态，Web版本暂时为空
          questionForHumanReading: content, // 人类可读的问题
          contextItems: session.config.contextItems || [], // 上下文项目
          editingMessageTs: undefined, // 编辑消息时间戳，新消息为空
        });

        this.logger.debug(`收到AI服务响应: ${JSON.stringify(response).slice(0, 200)}...`);

        // 从响应中提取AI回复内容
        let assistantContent = '';
        if (response.status === 'ok' && response.data) {
          // 根据实际的响应格式解析内容
          if (typeof response.data === 'string') {
            assistantContent = response.data;
          } else if (response.data.content) {
            assistantContent = response.data.content;
          } else if (response.data.message) {
            assistantContent = response.data.message;
          } else {
            // 如果响应格式不符合预期，使用默认回复
            assistantContent = '抱歉，我无法理解您的问题，请重新表述。';
            this.logger.warn(`未知的AI服务响应格式: ${JSON.stringify(response.data)}`);
          }
        } else {
          assistantContent = '抱歉，AI服务暂时不可用，请稍后重试。';
          this.logger.error(`AI服务返回错误: ${JSON.stringify(response)}`);
        }

        const assistantMessage: Message = {
          role: 'assistant',
          content: assistantContent,
          id: uuid.v4(),
          createdAt: Date.now(),
        };

        session.messages.push(assistantMessage);
        session.updatedAt = Date.now();

        return assistantMessage;

      } catch (localServiceError) {
        // 如果本地服务不可用，返回错误信息
        this.logger.error(`本地AI服务调用失败: ${localServiceError}`);

        const errorMessage: Message = {
          role: 'assistant',
          content: '抱歉，AI服务暂时不可用。可能的原因：\n1. 本地代理服务未启动\n2. 二进制服务连接失败\n3. 网络连接问题\n\n请检查服务状态后重试。',
          id: uuid.v4(),
          createdAt: Date.now(),
        };

        session.messages.push(errorMessage);
        session.updatedAt = Date.now();

        return errorMessage;
      }
    } catch (error) {
      this.logger.error(`发送消息失败: ${sessionId}`, error);
      
      // 创建错误消息
      const errorMessage: Message = {
        role: 'assistant',
        content: `发送消息时出现错误: ${error}`,
        id: uuid.v4(),
        createdAt: Date.now(),
      };
      
      session.messages.push(errorMessage);
      session.updatedAt = Date.now();
      
      return errorMessage;
    }
  }

  /**
   * 简单问答接口
   */
  async ask(question: string): Promise<string> {
    try {
      // 创建临时会话进行问答
      const session = this.createSession({
        systemPrompt: '你是一个有用的AI助手，请简洁明了地回答用户的问题。',
      });

      this.logger.debug(`简单问答: ${question.slice(0, 50)}...`);

      const reply = await this.sendMessage(session.id, question);

      // 使用完后删除临时会话，避免占用内存
      this.deleteSession(session.id);

      return reply.content;
    } catch (error) {
      this.logger.error(`问答失败: ${question}`, error);
      return `发生错误: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * 流式问答接口
   */
  async *askStream(question: string): AsyncGenerator<string> {
    try {
      // 创建临时会话进行流式问答
      const session = this.createSession();

      // 添加用户消息
      const userMessage: Message = {
        role: 'user',
        content: question,
        id: uuid.v4(),
        createdAt: Date.now(),
      };
      session.messages.push(userMessage);

      this.logger.debug(`开始流式问答: ${question.slice(0, 50)}...`);

      try {
        // 发送流式请求到本地二进制服务，使用与 VSCode 插件相同的参数格式
        const response = await this.localService.request('assistant/agent/local', {
          type: 'streamTask', // 或者使用 'newTask' 如果不支持流式
          task: question,
          reqData: {
            sessionId: session.id,
            chatId: uuid.v4(),
          },
          rules: [],
          editorState: null,
          questionForHumanReading: question,
          contextItems: session.config.contextItems || [],
          editingMessageTs: undefined,
          stream: true, // 启用流式响应
        });

        if (response.status === 'ok') {
          // 如果支持流式响应，这里应该监听流式数据
          // 目前先模拟流式输出
          const fullResponse = response.data?.content || response.data || '抱歉，无法获取回答。';
          const words = fullResponse.split(' ');

          for (let i = 0; i < words.length; i++) {
            yield words.slice(0, i + 1).join(' ');
            await new Promise(resolve => setTimeout(resolve, 100)); // 模拟流式延迟
          }
        } else {
          yield '抱歉，AI服务暂时不可用。';
        }
      } catch (localServiceError) {
        this.logger.error(`流式AI服务调用失败: ${localServiceError}`);
        yield '抱歉，AI服务连接失败，请检查服务状态。';
      }

      // 清理临时会话
      this.deleteSession(session.id);

    } catch (error) {
      this.logger.error(`流式问答失败: ${question}`, error);
      yield `发生错误: ${error}`;
    }
  }

  /**
   * 更新会话配置
   */
  updateSessionConfig(sessionId: string, config: Partial<SessionConfig>): Session | undefined {
    const session = this.getSession(sessionId);
    if (!session) return undefined;

    session.config = { ...session.config, ...config };
    session.updatedAt = Date.now();

    return session;
  }

  /**
   * 检查本地AI服务状态
   */
  async checkLocalServiceStatus(): Promise<{ isRunning: boolean; message: string }> {
    try {
      // 尝试ping本地服务
      const response = await this.localService.request('state/agentState', undefined);

      if (response.status === 'ok') {
        return {
          isRunning: true,
          message: '本地AI服务运行正常',
        };
      } else {
        return {
          isRunning: false,
          message: `本地AI服务状态异常: ${response.status}`,
        };
      }
    } catch (error) {
      this.logger.error('检查本地服务状态失败', error);
      return {
        isRunning: false,
        message: `本地AI服务连接失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats() {
    return {
      totalSessions: this.sessions.size,
      activeSessions: Array.from(this.sessions.values()).filter(
        session => Date.now() - session.updatedAt < 24 * 60 * 60 * 1000 // 24小时内活跃
      ).length,
      totalMessages: Array.from(this.sessions.values()).reduce(
        (total, session) => total + session.messages.length, 0
      ),
      localServiceRunning: this.localService.checkIsRunning(),
    };
  }
}
