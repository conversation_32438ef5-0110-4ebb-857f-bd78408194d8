import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { LocalService } from './local-service';
import { ConfigService } from '../config/config.service';
import { ProjectService } from './project.service';

/**
 * 代理模块配置
 */
export interface AgentConfig {
  repoPath?: string;          // 代码仓库路径
  enableRepoIndex?: boolean;  // 是否启用代码索引
  maxIndexSpace?: number;     // 最大索引空间(GB)
  proxyUrl?: string;          // 代理URL
  agentPreference?: string;   // 代理偏好设置
  userName?: string;          // 用户名
}

/**
 * 索引状态
 */
export interface IndexState {
  indexed: boolean;           // 是否已索引
  indexing: boolean;          // 是否正在索引
  indexingProgress: number;   // 索引进度(0-1)
  indexingMessage: string;    // 索引消息
  lastBuildTime: string;      // 上次构建时间
  pauseIndexManual: boolean;  // 是否手动暂停索引
  status: 'indexing' | 'indexed' | 'paused'; // 状态
}

/**
 * 代理服务 - 负责处理AI代理相关功能
 */
@Injectable()
export class AgentService implements OnModuleInit {
  private readonly logger = new Logger(AgentService.name);
  
  private indexState: IndexState = {
    indexed: false,
    indexing: false,
    indexingProgress: 0,
    indexingMessage: '',
    lastBuildTime: '',
    pauseIndexManual: false,
    status: 'paused',
  };

  constructor(
    private readonly localService: LocalService,
    private readonly configService: ConfigService,
    private readonly projectService: ProjectService
  ) {}

  async onModuleInit() {
    this.logger.log('AgentService 模块初始化开始');

    // 延迟设置事件处理器，确保 LocalService 完全初始化
    setTimeout(() => {
      this.logger.log('延迟设置 AgentService 事件处理器');
      this.setupEventHandlers();
    }, 1000);

    // 等待本地服务准备好
    this.localService.on('ready', () => {
      this.logger.log('本地服务准备就绪，重新设置事件处理器并初始化代理服务');
      this.setupEventHandlers();
      this.initAgent();
    });

    this.localService.on('restart', () => {
      this.logger.log('本地服务重启，重新设置事件处理器并初始化代理服务');
      this.setupEventHandlers();
      this.initAgent();
    });

    // 检查本地服务是否已经运行
    if (this.localService.checkIsRunning()) {
      this.logger.log('本地服务已在运行，直接初始化代理服务');
      this.initAgent();
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    this.logger.log('🔧 设置 AgentService 事件处理器');

    // 检查 LocalService 是否可用
    if (!this.localService) {
      this.logger.error('❌ LocalService 不可用，无法设置事件处理器');
      return;
    }

    // 处理索引进度更新
    this.localService.onMessage('index/progress', (message) => {
      this.logger.debug('收到索引进度更新消息', message);
      if (message.data) {
        this.updateIndexProgress(message.data);
      }
      return { status: 'ok' };
    });

    // 处理环境信息请求
    this.localService.onMessage('state/ideInfo', (message) => {
      this.logger.log('🔍 收到 state/ideInfo 请求', message);

      const projectInfo = this.projectService.getProjectInfo();
      const response = {
        status: 'ok' as const,
        data: {
          pluginVersion: '1.0.0',
          version: '1.0.0',
          platform: 'vscode',
          device: 'kwaipilot-vscode',
          repoInfo: {
            git_url: projectInfo?.gitRemote || '',
            dir_path: projectInfo?.repoPath || this.configService.getRepoPath(),
            commit: projectInfo?.currentCommit || '',
            branch: projectInfo?.currentBranchName || '',
          },
          userInfo: {
            name: this.configService.getUserName(),
          },
          proxyUrl: this.configService.getProxyUrl(),
          maxIndexSpace: this.configService.getMaxIndexSpace(),
          cwd: process.cwd(),
        },
      };

      this.logger.log('📤 发送 state/ideInfo 响应', response);
      return response;
    });

    // 处理配置请求
    this.localService.onMessage('config/getIdeSetting', (message) => {
      this.logger.log('🔍 收到 config/getIdeSetting 请求', message);

      const response = {
        status: 'ok' as const,
        data: {
          dirPath: this.configService.getRepoPath(),
          fileRetryTime: 3,
          modelRetryTime: 3,
          enableRepoIndex: this.configService.isRepoIndexEnabled(),
          maxIndexSpace: this.configService.getMaxIndexSpace(),
          proxyUrl: this.configService.getProxyUrl(),
          agentPreference: this.configService.getAgentPreference(),
        },
      };

      this.logger.log('📤 发送 config/getIdeSetting 响应', response);
      return response;
    });

    // 处理状态请求
    this.localService.onMessage('state/ideState', (message) => {
      this.logger.log('🔍 收到 state/ideState 请求', message);
      const response = { status: 'ok' as const };
      this.logger.log('📤 发送 state/ideState 响应', response);
      return response;
    });

    this.logger.log('✅ AgentService 事件处理器设置完成');
  }

  /**
   * 手动重新注册事件处理器（用于调试）
   */
  public reregisterEventHandlers() {
    this.logger.log('🔄 手动重新注册 AgentService 事件处理器');
    this.setupEventHandlers();
  }

  /**
   * 初始化代理服务
   */
  private async initAgent() {
    try {
      this.logger.log('初始化代理服务');
      
      // 检查仓库状态
      const res = await this.localService.request('state/checkRepoState', undefined);
      const shouldBuildIndex = this.configService.isRepoIndexEnabled() && !res.data?.isPaused;
      
      if (shouldBuildIndex) {
        if (res.status === 'ok' && res.data?.progress === 1) {
          this.updateIndexProgress({
            progress: 1,
            indexed: true,
          });
          this.localService.sendMessage('index/build', undefined);
        } else {
          this.localService.sendMessage('index/repoIndex', undefined);
          this.indexState.indexing = true;
          this.indexState.pauseIndexManual = false;
          this.indexState.status = 'indexing';
        }
      } else {
        this.indexState.indexing = false;
        this.indexState.indexingProgress = 0;
        this.indexState.indexingMessage = '';
        this.indexState.lastBuildTime = '';
        this.indexState.pauseIndexManual = false;
        this.indexState.indexed = true;
        this.indexState.status = 'indexed';
      }
    } catch (error) {
      this.logger.error('初始化代理服务失败', error);
    }
  }

  /**
   * 更新索引进度
   */
  updateIndexProgress(data: { progress: number; indexed?: boolean }) {
    this.indexState.indexingProgress = data.progress;
    
    if (data.indexed !== undefined) {
      this.indexState.indexed = data.indexed;
    }
    
    if (data.progress >= 1) {
      this.indexState.indexing = false;
      this.indexState.indexed = true;
      this.indexState.status = 'indexed';
      this.indexState.lastBuildTime = new Date().toISOString();
      
      this.logger.log('索引构建完成');
    } else if (data.progress > 0) {
      this.indexState.indexing = true;
      this.indexState.status = 'indexing';
      
      this.logger.log(`索引构建进度: ${Math.floor(data.progress * 100)}%`);
    }
  }

  /**
   * 获取代理配置
   */
  getConfig(): AgentConfig {
    return {
      repoPath: this.configService.getRepoPath(),
      enableRepoIndex: this.configService.isRepoIndexEnabled(),
      maxIndexSpace: this.configService.getMaxIndexSpace(),
      proxyUrl: this.configService.getProxyUrl(),
      agentPreference: this.configService.getAgentPreference(),
      userName: this.configService.getUserName(),
    };
  }

  /**
   * 获取索引状态
   */
  getIndexState(): IndexState {
    return this.indexState;
  }

  /**
   * 执行文本搜索
   */
  async search(query: string, limit = 20) {
    try {
      const result = await this.localService.request('search/search', { query, limit });
      return result;
    } catch (error) {
      this.logger.error(`搜索失败: ${query}`, error);
      throw error;
    }
  }

  /**
   * 开始构建索引
   */
  startRepoIndex() {
    this.localService.sendMessage('index/repoIndex', undefined);
    this.indexState.indexing = true;
    this.indexState.pauseIndexManual = false;
    this.indexState.status = 'indexing';
    return this.indexState;
  }

  /**
   * 清除索引
   */
  clearIndex() {
    this.localService.sendMessage('index/clearIndex', undefined);
    this.indexState.indexed = false;
    this.indexState.indexing = false;
    this.indexState.indexingProgress = 0;
    this.indexState.status = 'paused';
    return this.indexState;
  }

  /**
   * 检查代理状态
   */
  async checkAgentState() {
    try {
      return await this.localService.request('state/agentState', undefined);
    } catch (error) {
      this.logger.error('检查代理状态失败', error);
      throw error;
    }
  }
}
