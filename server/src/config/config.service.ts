import { Injectable } from '@nestjs/common';
import * as path from 'path';

/**
 * 配置服务 - 管理应用配置
 */
@Injectable()
export class ConfigService {
  private readonly config: Record<string, any> = {};

  constructor() {
    this.loadConfig();
  }

  private loadConfig() {
    // 服务配置
    this.config.PORT = this.getEnvNumber('PORT', 3001);
    this.config.LOG_LEVEL = this.getEnvString('LOG_LEVEL', 'info');

    // 二进制服务配置
    this.config.BINARY_PATH = this.getEnvString(
      'BINARY_PATH', 
      path.resolve(__dirname, '../../../local-agent/kwaipilot-binary')
    );
    this.config.BINARY_CWD = this.getEnvString(
      'BINARY_CWD', 
      path.resolve(__dirname, '../../../local-agent')
    );

    // 代理配置
    this.config.REPO_PATH = this.getEnvString('REPO_PATH', process.cwd());
    this.config.ENABLE_REPO_INDEX = this.getEnvBoolean('ENABLE_REPO_INDEX', true);
    this.config.MAX_INDEX_SPACE = this.getEnvNumber('MAX_INDEX_SPACE', 10);
    this.config.PROXY_URL = this.getEnvString('PROXY_URL', '');
    this.config.AGENT_PREFERENCE = this.getEnvString('AGENT_PREFERENCE', 'intelligent');
    this.config.USER_NAME = this.getEnvString('USER_NAME', 'deepsearch-user');

    // CORS 配置
    this.config.CORS_ORIGIN = this.getEnvString('CORS_ORIGIN', '*');
    this.config.CORS_CREDENTIALS = this.getEnvBoolean('CORS_CREDENTIALS', false);

    // 健康检查配置
    this.config.HEALTH_CHECK_INTERVAL = this.getEnvNumber('HEALTH_CHECK_INTERVAL', 600000);
    this.config.HEALTH_CHECK_TIMEOUT = this.getEnvNumber('HEALTH_CHECK_TIMEOUT', 180000);

    // 重启配置
    this.config.MAX_RESTART_ATTEMPTS = this.getEnvNumber('MAX_RESTART_ATTEMPTS', 10);
    this.config.RESTART_DELAY = this.getEnvNumber('RESTART_DELAY', 5000);
  }

  private getEnvString(key: string, defaultValue: string): string {
    return process.env[key] || defaultValue;
  }

  private getEnvNumber(key: string, defaultValue: number): number {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  private getEnvBoolean(key: string, defaultValue: boolean): boolean {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    return value.toLowerCase() === 'true';
  }

  /**
   * 获取配置值
   */
  get<T = any>(key: string): T {
    return this.config[key];
  }

  /**
   * 设置配置值
   */
  set(key: string, value: any): void {
    this.config[key] = value;
  }

  /**
   * 获取所有配置
   */
  getAll(): Record<string, any> {
    return { ...this.config };
  }

  /**
   * 获取服务端口
   */
  getPort(): number {
    return this.get<number>('PORT');
  }

  /**
   * 获取二进制服务路径
   */
  getBinaryPath(): string {
    return this.get<string>('BINARY_PATH');
  }

  /**
   * 获取二进制服务工作目录
   */
  getBinaryCwd(): string {
    return this.get<string>('BINARY_CWD');
  }

  /**
   * 获取仓库路径
   */
  getRepoPath(): string {
    return this.get<string>('REPO_PATH');
  }

  /**
   * 是否启用仓库索引
   */
  isRepoIndexEnabled(): boolean {
    return this.get<boolean>('ENABLE_REPO_INDEX');
  }

  /**
   * 获取最大索引空间
   */
  getMaxIndexSpace(): number {
    return this.get<number>('MAX_INDEX_SPACE');
  }

  /**
   * 获取代理URL
   */
  getProxyUrl(): string {
    return this.get<string>('PROXY_URL');
  }

  /**
   * 获取代理偏好
   */
  getAgentPreference(): string {
    return this.get<string>('AGENT_PREFERENCE');
  }

  /**
   * 获取用户名
   */
  getUserName(): string {
    return this.get<string>('USER_NAME');
  }

  /**
   * 获取CORS配置
   */
  getCorsConfig() {
    return {
      origin: this.get<string>('CORS_ORIGIN'),
      credentials: this.get<boolean>('CORS_CREDENTIALS'),
    };
  }

  /**
   * 获取健康检查配置
   */
  getHealthCheckConfig() {
    return {
      interval: this.get<number>('HEALTH_CHECK_INTERVAL'),
      timeout: this.get<number>('HEALTH_CHECK_TIMEOUT'),
    };
  }

  /**
   * 获取重启配置
   */
  getRestartConfig() {
    return {
      maxAttempts: this.get<number>('MAX_RESTART_ATTEMPTS'),
      delay: this.get<number>('RESTART_DELAY'),
    };
  }
}
